# RT预测系统 (Runtime Prediction System) - PyTorch版本

## 📋 项目概述

RT预测系统是一个基于PyTorch深度学习的半导体制造运行时间预测平台，能够根据设备条件、工艺配方和数量等参数预测加工运行时间，为生产规划和资源优化提供数据支持。

### 🎯 主要功能

- **多数据源支持**：模拟数据、CSV文件、MySQL数据库
- **智能预测**：基于PyTorch深度学习的RT预测模型
- **数据分析**：全面的数据探索和可视化分析
- **缺失数据补全**：自动预测缺失的条件组合数据
- **结果管理**：支持CSV和MySQL双重结果保存
- **数据质量保证**：自动处理重复数据，取中位数

### 🏗️ 系统架构

```
RT预测系统 (PyTorch版)
├── 数据层
│   ├── 模拟数据生成
│   ├── CSV文件读取
│   └── MySQL数据库
├── 模型层
│   ├── 数据预处理
│   ├── 特征工程
│   └── PyTorch深度学习模型
├── 分析层
│   ├── 数据探索分析
│   ├── 模型性能评估
│   └── 结果可视化
└── 应用层
    ├── 预测服务
    ├── 缺失数据补全
    └── 结果导出
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- PyTorch 2.0+
- MySQL 5.7+ (可选)

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

#### 1. 使用模拟数据快速体验

```bash
python sprs_rt_pytorch.py
```

选择数据源：`1` (模拟数据)

#### 2. 使用MySQL数据库

1. 配置数据库连接：
```json
{
    "host": "localhost",
    "port": 3306,
    "user": "root", 
    "password": "your_password",
    "database": "gtxsemi",
    "table_name": "sprs_rt_data"
}
```

2. 运行程序：
```bash
python sprs_rt_pytorch.py
```

选择数据源：`2` (MySQL数据库)

#### 3. 使用CSV文件

```bash
python sprs_rt_pytorch.py
```

选择数据源：`3` (CSV文件)，然后输入文件路径

## 📊 数据格式

### 输入数据格式

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| equip | string | 设备编号 | EQ001 |
| sub_equip | string | 子设备编号 | SUB_A |
| capability | string | 设备能力等级 | CAP_HIGH |
| recipe | string | 工艺配方 | RECIPE_1 |
| qty | int | 数量（片数） | 10 |
| rt | float | 运行时间（小时） | 15.5 |

### 输出结果格式

| 字段名 | 类型 | 说明 |
|--------|------|------|
| equip | string | 设备编号 |
| sub_equip | string | 子设备编号 |
| capability | string | 设备能力等级 |
| recipe | string | 工艺配方 |
| qty | int | 数量 |
| rt | float | 运行时间 |
| result_type | string | 数据类型：actual/predicted |
| timestamp | datetime | 时间戳 |

## 🔧 核心功能

### 1. 数据加载与预处理

```python
from sprs_rt_pytorch import load_your_data

# 加载MySQL数据
df = load_your_data(
    data_source="mysql",
    config_file="mysql_config.json"
)

# 加载CSV数据
df = load_your_data(
    data_source="csv", 
    csv_file_path="data.csv"
)
```

### 2. 模型训练与预测

```python
from sprs_rt_pytorch import RTPredictor

# 创建预测器
predictor = RTPredictor()

# 训练模型
predictor.train(df)

# 进行预测
test_data = pd.DataFrame([{
    'equip': 'EQ001',
    'sub_equip': 'SUB_A', 
    'capability': 'CAP_HIGH',
    'recipe': 'RECIPE_1',
    'qty': 15
}])

predictions = predictor.predict(test_data)
```

## 🆚 与TensorFlow版本的主要区别

### 技术栈差异
- **深度学习框架**：PyTorch 替代 TensorFlow/Keras
- **模型定义**：使用 `nn.Module` 类定义模型
- **训练循环**：手动实现训练循环，更灵活的控制
- **优化器**：使用 `torch.optim` 优化器
- **数据加载**：使用 `DataLoader` 进行批量数据处理

### 性能优势
- **动态计算图**：更灵活的模型调试和修改
- **内存效率**：更好的内存管理和GPU利用率
- **训练速度**：在某些场景下训练速度更快
- **模型部署**：更容易的模型导出和部署

### 代码特点
- **更直观的API**：PyTorch的API更接近Python原生语法
- **调试友好**：可以使用标准Python调试工具
- **研究导向**：更适合研究和实验新的模型架构

## 📁 项目结构

```
rt-prediction-system-pytorch/
├── sprs_rt_pytorch.py         # 主程序文件
├── rt_prediction_model_pytorch.py  # PyTorch深度学习模型
├── data_analysis_pytorch.py   # 数据分析模块
├── mysql_config.json          # MySQL配置文件
├── requirements.txt           # 依赖包列表
├── create_rt_table.sql        # 数据库表创建脚本
├── README.md                  # 项目说明文档
├── outputs/                   # 输出目录
│   ├── data/                  # 数据文件
│   ├── models/                # 保存的模型
│   ├── plots/                 # 图表文件
│   └── predictions/           # 预测结果
└── __pycache__/               # Python缓存文件
```

## 🔍 PyTorch模型特性

### 1. 灵活的模型架构
- 支持动态网络结构调整
- 易于添加新的层和组件
- 支持自定义损失函数和优化策略

### 2. 高效的训练过程
- 自动梯度计算
- GPU加速支持
- 混合精度训练
- 学习率调度

### 3. 模型保存和加载
- 支持完整模型保存
- 支持仅保存模型参数
- 跨平台模型兼容性

## 🛠️ 故障排除

### 常见问题

#### 1. PyTorch安装问题
```
错误：No module named 'torch'
解决：pip install torch torchvision torchaudio
```

#### 2. CUDA支持问题
```
错误：CUDA not available
解决：安装CUDA版本的PyTorch或使用CPU版本
```

#### 3. 内存不足
```
错误：CUDA out of memory
解决：减少batch_size或使用梯度累积
```

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues：[GitHub Issues](https://github.com/your-repo/rt-prediction-system-pytorch/issues)
- 邮箱：<EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！
