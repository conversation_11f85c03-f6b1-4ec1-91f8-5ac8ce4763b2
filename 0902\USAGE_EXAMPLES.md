# RT预测系统使用示例 - PyTorch版本

## 🚀 快速开始示例

### 1. 基本预测流程

```python
# 导入必要模块
from sprs_rt_pytorch import load_your_data, RTPredictor
from rt_prediction_model_pytorch import generate_sample_data

# 步骤1: 加载数据
df = load_your_data(
    data_source="mysql",  # 或 "csv", "simulated"
    config_file="mysql_config.json"
)

# 步骤2: 创建并训练预测器
predictor = RTPredictor()
predictor.train(df)

# 步骤3: 进行预测
test_data = pd.DataFrame([{
    'equip': 'EQ001',
    'sub_equip': 'SUB_A',
    'capability': 'CAP_HIGH',
    'recipe': 'RECIPE_1',
    'qty': 15
}])

predictions = predictor.predict(test_data)
print(f"预测RT: {predictions[0]:.2f} 小时")
```

### 2. 使用模拟数据快速测试

```python
import pandas as pd
from rt_prediction_model_pytorch import RTPredictor, generate_sample_data

# 生成模拟数据
df = generate_sample_data(1000)
print(f"生成了 {len(df)} 条模拟数据")

# 训练模型
predictor = RTPredictor()
results = predictor.train(df, epochs=50)

# 查看训练结果
print(f"MAE: {results['mae']:.4f}")
print(f"RMSE: {results['rmse']:.4f}")
print(f"R²: {results['r2']:.4f}")
```

### 3. 批量预测

```python
# 准备多个预测样本
test_cases = pd.DataFrame([
    {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 10},
    {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 15},
    {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 20},
    {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2', 'qty': 12},
    {'equip': 'EQ003', 'sub_equip': 'SUB_C', 'capability': 'CAP_LOW', 'recipe': 'RECIPE_3', 'qty': 8}
])

# 批量预测
predictions = predictor.predict(test_cases)

# 显示结果
for i, (_, row) in enumerate(test_cases.iterrows()):
    print(f"条件: {row['equip']}-{row['sub_equip']}-{row['capability']}-{row['recipe']}, qty={row['qty']}")
    print(f"预测RT: {predictions[i]:.2f} 小时\n")
```

## 🔧 高级功能示例

### 1. 自定义模型参数

```python
from rt_prediction_model_pytorch import RTPredictor
import torch

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 创建预测器并指定设备
predictor = RTPredictor(device=device)

# 自定义训练参数
results = predictor.train(
    df,
    test_size=0.2,           # 测试集比例
    validation_split=0.2,    # 验证集比例
    epochs=100,              # 训练轮数
    batch_size=64,           # 批次大小
    learning_rate=0.001      # 学习率
)
```

### 2. 模型保存和加载

```python
# 训练并保存模型
predictor = RTPredictor()
predictor.train(df)
model_path = predictor.save_model("my_rt_model")
print(f"模型已保存到: {model_path}")

# 加载已保存的模型
new_predictor = RTPredictor()
new_predictor.load_model(model_path)

# 使用加载的模型进行预测
predictions = new_predictor.predict(test_data)
```

### 3. 训练历史可视化

```python
# 训练模型
predictor = RTPredictor()
results = predictor.train(df, epochs=100)

# 绘制训练历史
predictor.plot_training_history(save_plot=True, show_plot=True)

# 获取训练历史数据
history = predictor.get_training_history()
print(f"最终训练损失: {history['train_loss'][-1]:.6f}")
print(f"最终验证损失: {history['val_loss'][-1]:.6f}")
```

### 4. 模型性能评估

```python
# 在测试集上评估模型
test_df = df.sample(100)  # 随机选择100个样本作为测试集
metrics = predictor.evaluate(test_df)

print("模型性能评估:")
print(f"平均绝对误差 (MAE): {metrics['mae']:.4f}")
print(f"均方误差 (MSE): {metrics['mse']:.4f}")
print(f"均方根误差 (RMSE): {metrics['rmse']:.4f}")
print(f"决定系数 (R²): {metrics['r2']:.4f}")
```

## 📊 数据分析示例

### 1. 完整数据分析

```python
from data_analysis_pytorch import RTDataAnalyzer

# 创建分析器
analyzer = RTDataAnalyzer()

# 执行完整分析
results = analyzer.analyze_rt_data(
    df, 
    save_plots=True,
    output_dir="outputs/plots",
    show_plots=False
)

# 获取建模建议
strategy = analyzer.recommend_modeling_strategy()
print(f"建模策略建议: {strategy}")
```

### 2. 便捷分析函数

```python
from data_analysis_pytorch import analyze_rt_data

# 一键分析
results = analyze_rt_data(
    df,
    save_plots=True,
    output_dir="outputs/plots"
)
```

## 🗄️ 数据库操作示例

### 1. 从MySQL加载数据

```python
# 方法1: 使用配置文件
df = load_your_data(
    data_source="mysql",
    config_file="mysql_config.json"
)

# 方法2: 直接传递参数
df = load_your_data(
    data_source="mysql",
    host="localhost",
    port=3306,
    user="root",
    password="your_password",
    database="gtxsemi",
    table_name="sprs_rt_data"
)
```

### 2. 保存预测结果到MySQL

```python
from sprs_rt_pytorch import save_results_to_mysql

# 创建预测结果DataFrame
results_df = pd.DataFrame({
    'equip': ['EQ001', 'EQ002'],
    'sub_equip': ['SUB_A', 'SUB_B'],
    'capability': ['CAP_HIGH', 'CAP_MED'],
    'recipe': ['RECIPE_1', 'RECIPE_2'],
    'qty': [15, 20],
    'rt': [22.5, 28.3],
    'result_type': ['predicted', 'predicted']
})

# 保存到MySQL
success = save_results_to_mysql(
    results_df,
    table_name="rt_prediction_results",
    config_file="mysql_config.json"
)

if success:
    print("预测结果已成功保存到MySQL")
```

## 🔍 PyTorch特有功能

### 1. GPU加速训练

```python
import torch

# 检查CUDA可用性
if torch.cuda.is_available():
    print(f"CUDA设备数量: {torch.cuda.device_count()}")
    print(f"当前CUDA设备: {torch.cuda.current_device()}")
    print(f"设备名称: {torch.cuda.get_device_name()}")
    
    # 使用GPU训练
    predictor = RTPredictor(device=torch.device('cuda'))
else:
    print("CUDA不可用，使用CPU训练")
    predictor = RTPredictor(device=torch.device('cpu'))
```

### 2. 混合精度训练（高级）

```python
# 注意：这需要修改模型代码以支持混合精度
# 这里仅作为示例展示概念

import torch
from torch.cuda.amp import GradScaler, autocast

# 在实际使用中，需要在模型训练循环中添加autocast和GradScaler
# 这可以显著提高训练速度并减少内存使用
```

### 3. 模型架构自定义

```python
from rt_prediction_model_pytorch import RTNet

# 自定义网络架构
custom_model = RTNet(
    input_dim=10,  # 根据特征数量调整
    hidden_dims=[512, 256, 128, 64],  # 自定义隐藏层大小
    dropout_rates=[0.4, 0.3, 0.2, 0.1]  # 自定义dropout率
)

print(f"模型参数数量: {sum(p.numel() for p in custom_model.parameters())}")
```

## 🚨 错误处理示例

### 1. 数据验证错误处理

```python
try:
    df = load_your_data("mysql", config_file="mysql_config.json")
    df = validate_data(df)
except FileNotFoundError:
    print("配置文件未找到，使用模拟数据")
    df = generate_sample_data(1000)
except ValueError as e:
    print(f"数据验证错误: {e}")
    # 处理数据问题或退出
```

### 2. 模型训练错误处理

```python
try:
    predictor = RTPredictor()
    results = predictor.train(df)
except RuntimeError as e:
    if "CUDA out of memory" in str(e):
        print("GPU内存不足，切换到CPU训练")
        predictor = RTPredictor(device=torch.device('cpu'))
        results = predictor.train(df, batch_size=16)  # 减小批次大小
    else:
        raise e
```

## 📈 性能优化建议

### 1. 数据预处理优化

```python
# 对于大数据集，考虑使用数据采样
if len(df) > 10000:
    df_sample = df.sample(n=5000, random_state=42)
    print(f"数据集过大，使用采样数据: {len(df_sample)} 条")
else:
    df_sample = df
```

### 2. 训练参数调优

```python
# 根据数据集大小调整参数
data_size = len(df)

if data_size < 500:
    epochs, batch_size = 50, 16
elif data_size < 2000:
    epochs, batch_size = 100, 32
else:
    epochs, batch_size = 150, 64

print(f"数据量: {data_size}, 使用参数: epochs={epochs}, batch_size={batch_size}")
```

这些示例展示了PyTorch版本RT预测系统的各种使用方法，从基础的训练预测到高级的GPU加速和模型自定义。根据您的具体需求选择合适的示例进行参考。
