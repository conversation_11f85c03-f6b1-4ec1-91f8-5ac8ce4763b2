-- 创建RT预测数据库和表结构 - PyTorch版本
-- 请根据您的实际需求调整表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS gtxsemi 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE gtxsemi;

-- 创建RT记录表
CREATE TABLE IF NOT EXISTS sprs_rt_data (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    equip VARCHAR(50) NOT NULL COMMENT '设备编号',
    sub_equip VARCHAR(50) NOT NULL COMMENT '子设备编号',
    capability VARCHAR(50) NOT NULL COMMENT '设备能力等级',
    recipe VARCHAR(100) NOT NULL COMMENT '工艺配方',
    qty INT NOT NULL COMMENT '数量（片数）',
    rt DECIMAL(10,2) NOT NULL COMMENT '运行时间（小时）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 添加索引以提高查询性能
    INDEX idx_equip (equip),
    INDEX idx_sub_equip (sub_equip),
    INDEX idx_capability (capability),
    INDEX idx_recipe (recipe),
    INDEX idx_qty (qty),
    INDEX idx_rt (rt),
    INDEX idx_condition (equip, sub_equip, capability, recipe),
    
    -- 添加约束
    CONSTRAINT chk_qty_positive CHECK (qty > 0),
    CONSTRAINT chk_rt_positive CHECK (rt > 0)
) ENGINE=InnoDB 
COMMENT='RT预测模型训练数据表 - PyTorch版本';

-- 创建预测结果表
CREATE TABLE IF NOT EXISTS rt_prediction_results (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    equip VARCHAR(50) NOT NULL COMMENT '设备编号',
    sub_equip VARCHAR(50) NOT NULL COMMENT '子设备编号',
    capability VARCHAR(50) NOT NULL COMMENT '设备能力等级',
    recipe VARCHAR(100) NOT NULL COMMENT '工艺配方',
    qty INT NOT NULL COMMENT '数量（片数）',
    rt DECIMAL(10,2) NOT NULL COMMENT '运行时间（小时）',
    result_type ENUM('actual', 'predicted') NOT NULL DEFAULT 'predicted' COMMENT '数据类型',
    model_version VARCHAR(50) DEFAULT 'pytorch_v1.0' COMMENT '模型版本',
    confidence_score DECIMAL(5,4) DEFAULT NULL COMMENT '置信度分数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 添加索引
    INDEX idx_result_type (result_type),
    INDEX idx_model_version (model_version),
    INDEX idx_condition_result (equip, sub_equip, capability, recipe, result_type),
    
    -- 添加唯一约束（防止重复预测结果）
    UNIQUE KEY uk_condition_qty (equip, sub_equip, capability, recipe, qty, result_type)
) ENGINE=InnoDB 
COMMENT='RT预测结果表 - PyTorch版本';

-- 插入示例数据（可选）
INSERT INTO sprs_rt_data (equip, sub_equip, capability, recipe, qty, rt) VALUES
('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_1', 10, 15.50),
('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_1', 15, 22.75),
('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_1', 20, 29.80),
('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_2', 10, 18.20),
('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_2', 15, 26.40),
('EQ002', 'SUB_B', 'CAP_MED', 'RECIPE_1', 10, 17.80),
('EQ002', 'SUB_B', 'CAP_MED', 'RECIPE_1', 15, 25.90),
('EQ002', 'SUB_B', 'CAP_MED', 'RECIPE_2', 10, 20.50),
('EQ003', 'SUB_C', 'CAP_LOW', 'RECIPE_3', 5, 12.30),
('EQ003', 'SUB_C', 'CAP_LOW', 'RECIPE_3', 10, 23.60),
('EQ004', 'SUB_A', 'CAP_HIGH', 'RECIPE_4', 8, 19.40),
('EQ004', 'SUB_A', 'CAP_HIGH', 'RECIPE_4', 12, 27.60),
('EQ005', 'SUB_B', 'CAP_MED', 'RECIPE_3', 6, 14.80),
('EQ005', 'SUB_B', 'CAP_MED', 'RECIPE_3', 18, 31.20);

-- 查看表结构
DESCRIBE sprs_rt_data;
DESCRIBE rt_prediction_results;

-- 查看示例数据
SELECT * FROM sprs_rt_data LIMIT 10;

-- 统计信息查询
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT equip) as unique_equips,
    COUNT(DISTINCT sub_equip) as unique_sub_equips,
    COUNT(DISTINCT capability) as unique_capabilities,
    COUNT(DISTINCT recipe) as unique_recipes,
    MIN(qty) as min_qty,
    MAX(qty) as max_qty,
    MIN(rt) as min_rt,
    MAX(rt) as max_rt,
    AVG(rt) as avg_rt
FROM sprs_rt_data;
