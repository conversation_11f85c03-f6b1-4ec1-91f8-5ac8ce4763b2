import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict, Any, Optional
import warnings
import os
from datetime import datetime
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
import matplotlib
import platform

# 全局设置，解决负号显示问题
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
# 设置更多字体相关参数
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['mathtext.fontset'] = 'stix'
matplotlib.rcParams['mathtext.default'] = 'regular'

def setup_chinese_font():
    """配置matplotlib中文字体"""
    # 设置全局字体配置，解决负号显示问题
    matplotlib.rcParams['axes.unicode_minus'] = False
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']

    system = platform.system()
    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']

    for font in fonts:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
            matplotlib.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"使用字体: {font}")
            break
        except:
            continue
    else:
        print("警告: 未找到合适的中文字体，中文可能显示为方框")
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False

# 初始化字体配置
setup_chinese_font()

def get_output_path(filename: str, subfolder: str = "") -> str:
    """获取输出文件路径"""
    # 动态获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.join(script_dir, "outputs")

    if subfolder:
        output_dir = os.path.join(base_dir, subfolder)
    else:
        output_dir = base_dir

    os.makedirs(output_dir, exist_ok=True)

    # 添加时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    name, ext = os.path.splitext(filename)
    filename_with_timestamp = f"{name}_{timestamp}{ext}"

    return os.path.join(output_dir, filename_with_timestamp)

class RTNet(nn.Module):
    """
    PyTorch深度神经网络模型用于RT预测
    """
    
    def __init__(self, input_dim: int, hidden_dims: list = [256, 128, 64, 32], dropout_rates: list = [0.3, 0.2, 0.1, 0.1]):
        super(RTNet, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.dropout_rates = dropout_rates
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim, dropout_rate in zip(hidden_dims, dropout_rates):
            # 全连接层
            layers.append(nn.Linear(prev_dim, hidden_dim))
            # 批归一化
            layers.append(nn.BatchNorm1d(hidden_dim))
            # 激活函数
            layers.append(nn.ReLU())
            # Dropout
            layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        return self.network(x)

class RTPredictor:
    """
    运行时间预测模型 - PyTorch版本
    使用深度学习预测不同条件下的运行时间(rt)
    """
    
    def __init__(self, device=None):
        self.device = device if device else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.scaler_features = StandardScaler()
        self.scaler_target = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        self.training_history = {'train_loss': [], 'val_loss': []}
        self.condition_stats = None
        
        print(f"使用设备: {self.device}")
    
    def prepare_data(self, df: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        数据预处理和特征工程

        Args:
            df: 包含equip, sub_equip, capability, recipe, qty列的DataFrame，训练时还需要rt列
            is_training: 是否为训练模式

        Returns:
            处理后的特征矩阵和目标变量（预测时目标变量为None）
        """
        # 创建数据副本
        data = df.copy()

        # 1. 基础特征编码
        categorical_features = ['equip', 'sub_equip', 'capability', 'recipe']
        
        for feature in categorical_features:
            if is_training:
                # 训练时创建新的编码器
                le = LabelEncoder()
                data[feature] = le.fit_transform(data[feature].astype(str))
                self.label_encoders[feature] = le
            else:
                # 预测时使用已有的编码器
                if feature in self.label_encoders:
                    # 处理未见过的类别
                    le = self.label_encoders[feature]
                    unique_values = set(data[feature].astype(str))
                    known_values = set(le.classes_)
                    unknown_values = unique_values - known_values
                    
                    if unknown_values:
                        print(f"警告: {feature} 中发现未知类别: {unknown_values}")
                        # 将未知类别映射到第一个已知类别
                        data[feature] = data[feature].astype(str).apply(
                            lambda x: x if x in known_values else le.classes_[0]
                        )
                    
                    data[feature] = le.transform(data[feature].astype(str))
                else:
                    raise ValueError(f"未找到特征 {feature} 的编码器，请先训练模型")

        # 2. 数值特征
        numerical_features = ['qty']
        
        # 3. 特征工程
        # qty的对数变换（处理偏态分布）
        data['qty_log'] = np.log1p(data['qty'])
        
        # qty的平方根变换
        data['qty_sqrt'] = np.sqrt(data['qty'])
        
        # qty的二次项
        data['qty_squared'] = data['qty'] ** 2
        
        # 组合特征：设备和配方的交互
        data['equip_recipe'] = data['equip'] * 1000 + data['recipe']
        data['sub_equip_capability'] = data['sub_equip'] * 100 + data['capability']
        
        # 选择最终特征
        feature_columns = (categorical_features + numerical_features + 
                         ['qty_log', 'qty_sqrt', 'qty_squared', 'equip_recipe', 'sub_equip_capability'])
        
        if is_training:
            self.feature_columns = feature_columns
        
        X = data[feature_columns].values.astype(np.float32)
        
        # 目标变量处理
        if is_training and 'rt' in data.columns:
            y = data['rt'].values.astype(np.float32).reshape(-1, 1)
            return X, y
        else:
            return X, None

    def train(self, df: pd.DataFrame, test_size: float = 0.2,
              validation_split: float = 0.2, epochs: int = 100,
              batch_size: int = 32, learning_rate: float = 0.001) -> Dict[str, Any]:
        """
        训练模型

        Args:
            df: 训练数据
            test_size: 测试集比例
            validation_split: 验证集比例
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率

        Returns:
            训练结果字典
        """
        print("开始数据预处理...")
        # 数据预处理
        X, y = self.prepare_data(df, is_training=True)

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42
        )

        # 特征标准化
        X_train_scaled = self.scaler_features.fit_transform(X_train)
        X_test_scaled = self.scaler_features.transform(X_test)

        # 目标变量标准化
        y_train_scaled = self.scaler_target.fit_transform(y_train)
        y_test_scaled = self.scaler_target.transform(y_test)

        # 进一步划分训练集和验证集
        val_size = int(len(X_train_scaled) * validation_split)
        X_val = X_train_scaled[:val_size]
        y_val = y_train_scaled[:val_size]
        X_train_final = X_train_scaled[val_size:]
        y_train_final = y_train_scaled[val_size:]

        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train_final).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train_final).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val).to(self.device)
        X_test_tensor = torch.FloatTensor(X_test_scaled).to(self.device)
        y_test_tensor = torch.FloatTensor(y_test_scaled).to(self.device)

        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        # 构建模型
        input_dim = X_train_scaled.shape[1]
        self.model = RTNet(input_dim).to(self.device)

        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10)

        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        print("开始训练...")

        # 训练循环
        self.training_history = {'train_loss': [], 'val_loss': []}
        best_val_loss = float('inf')
        patience_counter = 0
        early_stopping_patience = 20

        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()

            train_loss /= len(train_loader)

            # 验证阶段
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_val_tensor)
                val_loss = criterion(val_outputs, y_val_tensor).item()

            # 记录历史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)

            # 学习率调度
            old_lr = optimizer.param_groups[0]['lr']
            scheduler.step(val_loss)
            new_lr = optimizer.param_groups[0]['lr']
            if old_lr != new_lr:
                print(f"学习率调整: {old_lr:.6f} -> {new_lr:.6f}")

            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1

            if epoch % 10 == 0:
                print(f'Epoch [{epoch}/{epochs}], Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

            if patience_counter >= early_stopping_patience:
                print(f"早停在第 {epoch} 轮，最佳验证损失: {best_val_loss:.6f}")
                break

        # 加载最佳模型
        self.model.load_state_dict(best_model_state)

        # 最终评估
        self.model.eval()
        with torch.no_grad():
            y_pred_scaled = self.model(X_test_tensor).cpu().numpy()
            y_pred = self.scaler_target.inverse_transform(y_pred_scaled).flatten()

        # 计算评估指标
        mae = mean_absolute_error(y_test.flatten(), y_pred)
        mse = mean_squared_error(y_test.flatten(), y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test.flatten(), y_pred)

        results = {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'y_test': y_test.flatten(),
            'y_pred': y_pred,
            'epochs_trained': epoch + 1
        }

        print(f"\n模型评估结果:")
        print(f"MAE: {mae:.4f}")
        print(f"RMSE: {rmse:.4f}")
        print(f"R²: {r2:.4f}")
        print(f"训练轮数: {epoch + 1}")

        return results

    def predict(self, df: pd.DataFrame) -> np.ndarray:
        """
        预测新数据

        Args:
            df: 包含特征的DataFrame

        Returns:
            预测的rt值
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train方法")

        X, _ = self.prepare_data(df, is_training=False)
        X_scaled = self.scaler_features.transform(X)
        X_tensor = torch.FloatTensor(X_scaled).to(self.device)

        self.model.eval()
        with torch.no_grad():
            y_pred_scaled = self.model(X_tensor).cpu().numpy()
            y_pred = self.scaler_target.inverse_transform(y_pred_scaled).flatten()

        return y_pred

    def save_model(self, filename: str) -> str:
        """
        保存模型

        Args:
            filename: 文件名（不含扩展名）

        Returns:
            保存的文件路径
        """
        if self.model is None:
            raise ValueError("没有可保存的模型")

        # 创建保存路径
        save_path = get_output_path(f"{filename}.pth", "models")

        # 保存模型状态
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_architecture': {
                'input_dim': self.model.input_dim,
                'hidden_dims': self.model.hidden_dims,
                'dropout_rates': self.model.dropout_rates
            },
            'scaler_features': self.scaler_features,
            'scaler_target': self.scaler_target,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns,
            'training_history': self.training_history,
            'condition_stats': self.condition_stats
        }, save_path)

        print(f"模型已保存到: {save_path}")
        return save_path

    def load_model(self, filepath: str):
        """
        加载模型

        Args:
            filepath: 模型文件路径
        """
        if not os.path.exists(filepath):
            # 尝试在当前脚本目录的outputs/models目录中查找
            script_dir = os.path.dirname(os.path.abspath(__file__))
            alt_path = os.path.join(script_dir, "outputs", "models", filepath)
            if os.path.exists(alt_path):
                filepath = alt_path
            else:
                raise FileNotFoundError(f"模型文件未找到: {filepath}")

        # 加载模型数据
        checkpoint = torch.load(filepath, map_location=self.device, weights_only=False)

        # 重建模型架构
        arch = checkpoint['model_architecture']
        self.model = RTNet(
            input_dim=arch['input_dim'],
            hidden_dims=arch['hidden_dims'],
            dropout_rates=arch['dropout_rates']
        ).to(self.device)

        # 加载模型参数
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # 加载其他组件
        self.scaler_features = checkpoint['scaler_features']
        self.scaler_target = checkpoint['scaler_target']
        self.label_encoders = checkpoint['label_encoders']
        self.feature_columns = checkpoint['feature_columns']
        self.training_history = checkpoint.get('training_history', {'train_loss': [], 'val_loss': []})
        self.condition_stats = checkpoint.get('condition_stats', None)

        print(f"模型已从 {filepath} 加载")

    def plot_training_history(self, save_plot: bool = True, show_plot: bool = False):
        """绘制训练历史并保存到outputs文件夹"""
        if not self.training_history or not self.training_history['train_loss']:
            print("没有训练历史可绘制")
            return

        # 确保字体配置生效
        import matplotlib
        matplotlib.rcParams['axes.unicode_minus'] = False

        plt.figure(figsize=(12, 4))

        # 损失曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.training_history['train_loss'], label='训练损失', color='blue')
        plt.plot(self.training_history['val_loss'], label='验证损失', color='red')
        plt.title('模型训练损失')
        plt.xlabel('轮数')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        # 学习曲线（对数尺度）
        plt.subplot(1, 2, 2)
        plt.semilogy(self.training_history['train_loss'], label='训练损失', color='blue')
        plt.semilogy(self.training_history['val_loss'], label='验证损失', color='red')
        plt.title('模型训练损失 (对数尺度)')
        plt.xlabel('轮数')
        plt.ylabel('损失 (对数)')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if save_plot:
            plot_path = get_output_path("training_history.png", "plots")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"训练历史图已保存到: {plot_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()

    def get_training_history(self) -> Dict[str, list]:
        """获取训练历史"""
        return self.training_history

    def evaluate(self, test_df: pd.DataFrame) -> Dict[str, float]:
        """
        评估模型性能

        Args:
            test_df: 测试数据

        Returns:
            评估指标字典
        """
        if self.model is None:
            raise ValueError("模型尚未训练")

        # 预测
        y_pred = self.predict(test_df)
        y_true = test_df['rt'].values

        # 计算指标
        mae = mean_absolute_error(y_true, y_pred)
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)

        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2
        }

def generate_sample_data(n_samples: int = 1000) -> pd.DataFrame:
    """
    生成示例数据用于测试

    Args:
        n_samples: 样本数量

    Returns:
        包含示例数据的DataFrame
    """
    np.random.seed(42)

    # 定义可能的值
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004', 'EQ005']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4']
    qtys = list(range(1, 51))  # 1-50片

    data = []

    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.choice(qtys)

        # 基础运行时间（小时）
        base_rt = 10.0

        # 设备因子
        equip_factors = {'EQ001': 1.0, 'EQ002': 1.2, 'EQ003': 0.9, 'EQ004': 1.1, 'EQ005': 0.95}
        equip_factor = equip_factors[equip]

        # 子设备因子
        sub_equip_factors = {'SUB_A': 1.0, 'SUB_B': 1.15, 'SUB_C': 0.85}
        sub_equip_factor = sub_equip_factors[sub_equip]

        # 能力因子
        cap_factors = {'CAP_HIGH': 0.8, 'CAP_MED': 1.0, 'CAP_LOW': 1.3}
        cap_factor = cap_factors[capability]

        # 配方因子
        recipe_factors = {'RECIPE_1': 1.0, 'RECIPE_2': 1.25, 'RECIPE_3': 0.75, 'RECIPE_4': 1.4}
        recipe_factor = recipe_factors[recipe]

        # qty因子 - 确保正相关，考虑批量效应
        linear_factor = qty * 1.0  # 线性部分

        # 批量效应：大批量时有轻微的效率提升
        if qty <= 5:
            batch_efficiency = 1.0
        elif qty <= 15:
            batch_efficiency = 0.95
        else:
            batch_efficiency = 0.90

        # 设置和准备时间（固定成本）
        setup_time = 2.0 * equip_factor * sub_equip_factor * cap_factor

        # 总时间 = 设置时间 + (每片时间 * 片数 * 批量效率)
        processing_time = base_rt * recipe_factor * linear_factor * batch_efficiency
        rt = setup_time + processing_time

        # 添加合理的随机噪声（±5%）
        noise_factor = np.random.normal(1.0, 0.05)
        rt = rt * noise_factor

        # 确保rt为正数且合理
        rt = max(rt, 1.0)
        rt = min(rt, 500.0)

        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt
        })

    return pd.DataFrame(data)

if __name__ == "__main__":
    print("=== PyTorch RT预测模型测试 ===")

    # 生成示例数据
    print("生成示例数据...")
    df = generate_sample_data(1000)
    print("示例数据:")
    print(df.head())
    print(f"\n数据形状: {df.shape}")

    # 训练模型
    print("\n训练模型...")
    predictor = RTPredictor()
    results = predictor.train(df, epochs=50)

    # 绘制训练历史
    predictor.plot_training_history()

    # 预测示例
    test_data = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 15},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2', 'qty': 20}
    ])

    predictions = predictor.predict(test_data)
    print(f"\n预测结果:")
    for i, pred in enumerate(predictions):
        print(f"样本 {i+1}: 预测rt = {pred:.2f}")

    # 保存模型
    model_path = predictor.save_model("rt_prediction_model_pytorch")

    # 测试模型加载
    print("\n测试模型加载...")
    new_predictor = RTPredictor()
    new_predictor.load_model(model_path)

    # 验证加载的模型
    new_predictions = new_predictor.predict(test_data)
    print("加载模型后的预测结果:")
    for i, pred in enumerate(new_predictions):
        print(f"样本 {i+1}: 预测rt = {pred:.2f}")

    print("\n=== 测试完成 ===")
