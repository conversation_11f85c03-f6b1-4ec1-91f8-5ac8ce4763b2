# PyTorch深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 机器学习工具
scikit-learn>=1.1.0

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 统计分析
scipy>=1.9.0

# 模型保存
joblib>=1.2.0

# 可选：进度条
tqdm>=4.64.0

# 可选：更好的数据类型支持
pyarrow>=9.0.0

# 可选：Jupyter notebook支持
jupyter>=1.0.0
ipykernel>=6.15.0

# 数据库连接
pymysql>=1.0.0
sqlalchemy>=1.4.0

# PyTorch相关工具
tensorboard>=2.10.0  # 用于训练可视化
